import { cookieIsolationManager } from './CookieIsolationManager'
import { GM_cookie, GM_xmlhttpRequest } from '$'

interface Response {
  data: any
  code: number
  msg: string
}

export class GMRequest {
  getByToken(url: string, token: string, preserveMainCookie = false) {
    return new Promise<Response>((resolve, reject) => {
      // 检查是否处于Cookie隔离模式
      const isInIsolationMode = cookieIsolationManager.isInIsolationMode()
      const requestId = Math.random().toString(36).substring(2, 8)
      const startTime = Date.now()

      // 详细的请求日志
      console.log(`[GMRequest-${requestId}] 发起请求:`, {
        url,
        token: token ? `${token.substring(0, 8)}...` : 'empty',
        preserveMainCookie,
        isInIsolationMode,
        cookieString: `m_gray_switch=1; m_gray_switch_=1; ts_session_id=${token.substring(0, 8)}...; ts_session_id_=${token.substring(0, 8)}...`,
      })

      // 如果处于隔离模式，记录状态
      if (isInIsolationMode)
        console.log(`[GMRequest-${requestId}] Cookie隔离模式激活`)

      GM_xmlhttpRequest({
        method: 'GET',
        url,
        cookie: `m_gray_switch=1; m_gray_switch_=1; ts_session_id=${token}; ts_session_id_=${token}`,
        headers: {
          'Content-Type': 'application/json',
        },
        onload(response) {
          const endTime = Date.now()
          const duration = endTime - startTime

          // 详细的响应日志
          console.log(`[GMRequest-${requestId}] 收到响应 (耗时 ${duration}ms):`, {
            status: response.status,
            statusText: response.statusText,
            responseHeaders: response.responseHeaders,
            responseTextLength: response.responseText?.length,
          })

          try {
            const parsedResponse = JSON.parse(response.responseText)
            console.log(`[GMRequest-${requestId}] 解析响应成功:`, {
              code: parsedResponse.code,
              msg: parsedResponse.msg,
              hasData: !!parsedResponse.data,
              dataKeys: parsedResponse.data ? Object.keys(parsedResponse.data) : [],
            })

            // 只清理灰度相关的cookie，保留主要的登录cookie
            GM_cookie.delete({ name: 'm_gray_token', url: '.seasunwbl.com' })
            GM_cookie.delete({ name: 'm_gray_token_', url: '.seasunwbl.com' })

            // Cookie清理策略：
            // 1. 如果明确要求保留主cookie（账号切换场景），则不清理主要cookie
            // 2. 如果处于Cookie隔离模式（多开抢号场景），则不清理主要cookie以避免冲突
            // 3. 其他情况按原逻辑清理
            const shouldPreserveCookie = preserveMainCookie || isInIsolationMode

            if (!shouldPreserveCookie) {
              console.log(`[GMRequest-${requestId}] 清理主要Cookie`)
              GM_cookie.delete({ name: 'm_gray_switch', url: '.seasunwbl.com' })
              GM_cookie.delete({ name: 'm_gray_switch_', url: '.seasunwbl.com' })
              GM_cookie.delete({ name: 'ts_session_id', url: '.seasunwbl.com' })
              GM_cookie.delete({ name: 'ts_session_id_', url: '.seasunwbl.com' })
            }
            else if (isInIsolationMode) {
              // 在隔离模式下，记录日志以便调试
              console.log(`[GMRequest-${requestId}] Cookie隔离模式：跳过清理主要cookie，当前请求token: ${token.substring(0, 8)}...`)
            }

            resolve(parsedResponse)
          }
          catch (parseError) {
            console.error(`[GMRequest-${requestId}] 响应解析失败:`, {
              parseError,
              responseText: response.responseText,
            })
            reject(new Error(`响应解析失败: ${parseError}`))
          }
        },
        onerror(error) {
          const endTime = Date.now()
          const duration = endTime - startTime

          console.error(`[GMRequest-${requestId}] 网络请求失败 (耗时 ${duration}ms):`, {
            error,
            url,
            token: token ? `${token.substring(0, 8)}...` : 'empty',
          })
          reject(error)
        },
      })
    })
  }

  // 专门用于账号切换的安全请求方法，不会清理主要的登录cookie
  getByTokenSafe(url: string, token: string) {
    return this.getByToken(url, token, true)
  }
}
